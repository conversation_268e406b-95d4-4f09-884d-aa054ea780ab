"use client";
import SummaryCards from "@/components/summary-cards";
import React, { useEffect } from "react";
import Lenis from "lenis";
import AboutJourney from "@/components/about-journey";

export default function Content() {
  useEffect(() => {
    const lenis = new Lenis();
    lenis.on("scroll", (e: any) => {
      console.log(e);
    });
    function raf(time: any) {
      lenis.raf(time);
      requestAnimationFrame(raf);
    }
    requestAnimationFrame(raf);
  }, []);

  return (
    <div className="space-y-4 px-4 pb-8">
      <SummaryCards />
      <AboutJourney />
    </div>
  );
}
