import {
  Timeline,
  TimelineContent,
  TimelineDate,
  TimelineHeader,
  TimelineIndicator,
  TimelineItem,
  TimelineSeparator,
  TimelineTitle,
} from "@/components/ui/timeline";
import React from "react";

const items = [
  {
    id: 1,
    date: "Mar 15, 2024",
    title: "Project Kickoff",
    description:
      "Initial team meeting and project scope definition. Established key milestones and resource allocation.",
  },
  {
    id: 2,
    date: "Mar 22, 2024",
    title: "Design Phase",
    description:
      "Completed wireframes and user interface mockups. Stakeholder review and feedback incorporated.",
  },
  {
    id: 3,
    date: "Apr 5, 2024",
    title: "Development Sprint",
    description:
      "Backend API implementation and frontend component development in progress.",
  },
  {
    id: 4,
    date: "Apr 19, 2024",
    title: "Testing & Deployment",
    description:
      "Quality assurance testing, performance optimization, and production deployment preparation.",
  },
];

export default function TimelineComponent() {
  return (
    <Timeline>
      {items.map((item) => (
        <TimelineItem key={item.id} step={item.id} className="pb-3">
          <TimelineHeader>
            <TimelineSeparator className="mt-4" />
            <TimelineDate className="text-zinc-400">{item.date}</TimelineDate>
            <TimelineTitle className="text-secondary-two">
              {item.title}
            </TimelineTitle>
            <TimelineIndicator className="border-secondary-two" />
          </TimelineHeader>
          <TimelineContent className="text-zinc-400">
            {item.description}
          </TimelineContent>
        </TimelineItem>
      ))}
    </Timeline>
  );
}
