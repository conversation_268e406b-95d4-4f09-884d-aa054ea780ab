import { defineField, defineType } from "sanity";

export const ProjectType = defineType({
  name: "project",
  title: "Project",
  type: "document",
  fields: [
    define<PERSON><PERSON>({
      name: "thumbnail",
      type: "image",
    }),
    define<PERSON>ield({
      name: "mockup",
      type: "image",
    }),
    define<PERSON>ield({
      name: "mockupType",
      type: "string",
      options: {
        list: [
          { title: "Laptop", value: "laptop" },
          { title: "Mobile", value: "mobile" },
        ], // <-- predefined values
        layout: "radio", // <-- defaults to 'dropdown'
      },
    }),
    define<PERSON>ield({
      name: "name",
      type: "string",
    }),
    defineField({
      name: "url",
      type: "string",
    }),
    defineField({
      name: "introduction",
      type: "array",
      of: [{ type: "block" }],
    }),
    defineField({
      name: "description",
      type: "array",
      of: [{ type: "block" }],
    }),
  ],
});
