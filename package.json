{"name": "convex", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emailjs/browser": "^4.3.3", "@hookform/resolvers": "^3.3.4", "@portabletext/react": "^3.0.18", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-tooltip": "^1.2.8", "@sanity/image-url": "^1.0.2", "@sanity/react-loader": "^1.9.18", "@sanity/vision": "^3.41.1", "@tabler/icons-react": "^3.1.0", "@tanstack/react-query": "^5.51.1", "@types/react-vertical-timeline-component": "^3.3.6", "@vercel/analytics": "^1.2.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^11.0.24", "lenis": "^1.1.6", "lottie-react": "^2.4.0", "lucide-react": "^0.364.0", "mini-svg-data-uri": "^1.4.4", "motion": "^12.23.12", "next": "14.1.4", "next-sanity": "^9.0.17", "next-themes": "^0.3.0", "prettier-plugin-tailwindcss": "^0.6.5", "radix-ui": "^1.4.3", "react": "^18", "react-dom": "^18", "react-fast-marquee": "^1.6.4", "react-hook-form": "^7.51.2", "react-intersection-observer": "^9.13.0", "react-vertical-timeline-component": "^3.6.0", "sanity": "^3.41.1", "sonner": "^1.4.41", "styled-components": "^6.1.8", "tailwind-merge": "^2.2.2", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.0", "zod": "^3.22.4"}, "devDependencies": {"@sanity/eslint-config-studio": "^4.0.0", "@tailwindcss/typography": "^0.5.13", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.1.4", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}